const User = require('../models/userModel');
const DoctorGuarantee = require('../models/doctorGuaranteeModel');

// ตรวจสอบว่า controller โครงสร้าง ต้อง
const DFController = {
  // Render doctor fee dashboard page
  renderDFDashboard: async (req, res) => {
    try {
      res.render('pages/df/doctor-fee-dashboard', {
        title: 'Doctor Fee System Dashboard',
        user: req.session.user,
        currentPage: 'dfDashboard'
      });
    } catch (error) {
      console.error('Error rendering doctor fee dashboard page:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to load doctor fee dashboard page',
        error: error.message
      });
    }
  },

  // Render doctor management page
  renderDFManagement: async (req, res) => {
    try {
      res.render('pages/df/doctor-management', {
        title: 'Doctor Management',
        user: req.session.user,
        currentPage: 'dfManagement'
      });
    } catch (error) {
      console.error('Error rendering doctor management page:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to load doctor management page',
        error: error.message
      });
    }
  },

  // Render doctor fee calculation page
  renderDFCal: async (req, res) => {
    try {
      res.render('pages/df/doctor-fee-cal', {
        title: 'Doctor Fee Calculation',
        user: req.session.user,
        currentPage: 'dfCal'
      });
    } catch (error) {
      console.error('Error rendering doctor fee calculation page:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to load doctor fee calculation page',
        error: error.message
      });
    }
  },

  // Render doctor fee guaranteen page
  renderDFGuaranteen: async (req, res) => {
    try {
        res.render('pages/df/doctor-fee-guaranteen', {
            title: 'Doctor Fee Guaranteen',
            user: req.session.user,
            currentPage: 'dfGuaranteen'
        });
    } catch (error) {
        console.error('Error rendering doctor fee guaranteen page:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to load doctor fee guaranteen page',
            error: error.message
        });
    }
},

  // Render doctor fee tax page
  renderDFTax: async (req, res) => {
    try {
        res.render('pages/df/doctor-fee-tax', {
            title: 'Doctor Fee Tax',
            user: req.session.user,
            currentPage: 'dfTax'
        });
    } catch (error) {
        console.error('Error rendering doctor fee tax page:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to load doctor fee tax page',
            error: error.message
        });

    }
  },

  // Render doctor fee report page
  renderDFReport: async (req, res) => {
    try {
        res.render('pages/df/doctor-fee-report', {
            title: 'Doctor Fee Report',
            user: req.session.user,
            currentPage: 'dfReport'
        });
    } catch (error) {
        console.error('Error rendering doctor fee report page:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to load doctor fee report page',
            error: error.message
        });
    }
  },

  // Get all users
  getAllUsers: async (req, res) => {
    try {
      const users = await User.getAll();
      
      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      console.error('Error getting all users:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get users',
        error: error.message
      });
    }
  },

  // Get user by ID
  getUserById: async (req, res) => {
    try {
      const { id } = req.params;
      const user = await User.getUserById(id);
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }
      
      // Remove password from response
      delete user.password;
      
      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      console.error('Error getting user by ID:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get user',
        error: error.message
      });
    }
  },


    // Get All Doctor
  getAllDoctors: async (req, res) => {
    try {
      const doctors = await User.getAllDoctors();
      
      res.json({
        success: true,
        data: doctors
      });
    } catch (error) {
      console.error('Error getting all doctors:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get doctors',
        error: error.message
      });
    }
  },

  // API endpoints for doctor guarantees
  createDoctorGuarantee: async (req, res) => {
    try {
      const { doctorId, guaranteeType, guaranteeAmount, effectiveDate, startdate, enddate, serviceTypes } = req.body;
      
      // ตรวจสอบข้อมูลการันตีแพทย์ที่จำเป็น
      if (!doctorId || !guaranteeType || !guaranteeAmount || !effectiveDate) {
        return res.status(400).json({
          success: false,
          message: 'กรุณากรอกข้อมูลให้ครบถ้วน' 
        });
      }
      
      // ใช้ ID ของผู้ใช้งานในการล็อกอิน อยู่
      const userId = req.session.user ? req.session.user.id : null;
      
      // สร้างการันตีแพทย์ใหม่หรืออัปเดตการันตีแพทย์ที่มีอยู่แล้ว
      const result = await DoctorGuarantee.create({
        doctorId,
        guaranteeType,
        guaranteeAmount,
        effectiveDate,
        startdate,
        enddate,
        serviceTypes
      }, userId);
      
      res.json({
        success: true,
        message: 'สร้างการันตีแพทย์แล้ว',
        data: result
      });
    } catch (error) {
      console.error('Error creating doctor guarantee:', error);
      res.status(500).json({
        success: false,
        message: 'ข้อผิดพลาดในการสร้างการันตีแพทย์',
        error: error.message
      });
    }
  },

  getDoctorGuarantees: async (req, res) => {
    try {
      const { doctorId } = req.params;
      
      // ดึงการันตีของแพทย์
      const guarantees = await DoctorGuarantee.getByDoctorId(doctorId);
      
      res.json({
        success: true,
        data: guarantees
      });
    } catch (error) {
      console.error('Error getting doctor guarantees:', error);
      res.status(500).json({
        success: false,
        message: 'ข้อผิดพลาดในการดึงข้อมูลการันตีแพทย์',
        error: error.message
      });
    }
  },

  getDoctorGuaranteeById: async (req, res) => {
    try {
      const { id } = req.params;
      
      // ยากข้อมูลการันยากตาม ID
      const guarantee = await DoctorGuarantee.getById(id);
      
      if (!guarantee) {
        return res.status(404).json({
          success: false,
          message: 'ไม่พบข้อมูลการันตีแพทย์'
        });
      }
      
      res.json({
        success: true,
        data: guarantee
      });
    } catch (error) {
      console.error('Error getting doctor guarantee by ID:', error);
      res.status(500).json({
        success: false,
        message: 'ข้อผิดพลาดในการดึงข้อมูลการันตีแพทย์',
        error: error.message
      });
    }
  },

  deleteDoctorGuarantee: async (req, res) => {
    try {
      const { id } = req.params;
      
      // ใช้ ID ของยาก้ใช้ยาก่ล็อกยากอยู่
      const userId = req.session.user ? req.session.user.id : null;
      
      // ลบการันยาก (soft delete)
      const result = await DoctorGuarantee.delete(id, userId);
      
      if (!result) {
        return res.status(404).json({
          success: false,
          message: 'ไม่พบข้อมูลการันตีแพทย์'
        });
      }
      
      res.json({
        success: true,
        message: 'ลบข้อมูลการันยากยากแล้ว'
      });
    } catch (error) {
      console.error('Error deleting doctor guarantee:', error);
      res.status(500).json({
        success: false,
        message: 'ยากข้อยากพลาดในการลบข้อมูลการันยาก',
        error: error.message
      });
    }
  },
}

module.exports = DFController;
