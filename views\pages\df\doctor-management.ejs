<!-- Doctor Management Header -->
<div class="row mb-3">
    <div class="col-12 d-flex justify-content-between align-items-center">
        <div>

            <h4 class="mb-1"> ข้อมูลแพทย์</h4>
            <p class="text-muted mb-0">ข้อมูลแพทย์และการตั้งค่าส่วนแบ่งรายได้</p>
        </div>
        <div>
            <button type="button" class="btn btn-dark" id="addDoctorBtn">
                <i class="ri-user-add-line me-1"></i> เลือกแพทย์ใหม่
            </button>
        </div>
    </div>
</div>

<!-- Doctor Management Tabs -->
<div class="row mb-3">
    <div class="col-12">
        <ul class="nav nav-pills nav-tabs-custom">
            <li class="nav-item">
                <a class="nav-link active" href="#">รายชื่อแพทย์</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">ตารางเวร</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#">การตั้งค่าส่วนแบ่ง</a>
            </li>
        </ul>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="search-box">
                            <input type="text" class="form-control search" placeholder="ค้นหาแพทย์...">
                            <i class="ri-search-line search-icon"></i>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="input-group">
                            <label class="input-group-text" for="filterSelect">ค้นหาหมด</label>
                            <select class="form-select" id="filterSelect">
                                <option selected>ค้นหาหมด</option>
                                <option value="fulltime">Fulltime</option>
                                <option value="parttime">Parttime</option>
                                <option value="consult">Consult</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Doctor Cards -->


<div class="row" id="doctorCardsContainer">
    <!-- ข้อมูลแพทย์จะถูกเพิ่มโดย JavaScript -->
</div>

<div class="row">
    <!-- Doctor 1 -->
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-sm me-3">
                        <div class="avatar-title bg-light text-primary rounded-circle">
                            <i class="ri-user-3-line fs-20"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-1">Dr. สมชาย ใจดี</h5>
                        <p class="text-muted mb-0">MD12345</p>
                    </div>
                    <div>
                        <span class="badge bg-soft-primary text-primary">Fulltime</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">ความเชี่ยวชาญ:</span>
                        <span>Internal Medicine</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">แผนก:</span>
                        <span>Internal Medicine</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">ส่วน:</span>
                        <span>25%</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">การันตี:</span>
                        <span class="fw-medium">฿120,000</span>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <span class="badge bg-soft-success text-success">Active</span>
                    <div>
                        <button class="btn btn-sm btn-light me-1" data-doctor-id="MD12345">
                            <i class="ri-pencil-line"></i>
                        </button>
                        <button class="btn btn-sm btn-light" data-doctor-id="MD12345">
                            <i class="ri-delete-bin-line text-danger"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Doctor 2 -->
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-sm me-3">
                        <div class="avatar-title bg-light text-primary rounded-circle">
                            <i class="ri-user-3-line fs-20"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-1">Dr. วิศาล รุ่งเรือง</h5>
                        <p class="text-muted mb-0">MD12346</p>
                    </div>
                    <div>
                        <span class="badge bg-soft-success text-success">Parttime</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">ความเชี่ยวชาญ:</span>
                        <span>Cardiology</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">แผนก:</span>
                        <span>Cardiology</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted"> 30%</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">การัน 30000:</span>
                        <span class="fw-medium">฿80,000</span>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <span class="badge bg-soft-success text-success">Active</span>
                    <div>
                        <button class="btn btn-sm btn-light me-1" data-doctor-id="MD12346">
                            <i class="ri-pencil-line"></i>
                        </button>
                        <button class="btn btn-sm btn-light" data-doctor-id="MD12346">
                            <i class="ri-delete-bin-line text-danger"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Doctor 3 -->
    <div class="col-lg-4 col-md-6 mb-3">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-sm me-3">
                        <div class="avatar-title bg-light text-primary rounded-circle">
                            <i class="ri-user-3-line fs-20"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-1">Dr. ประยุทธ์ เก่งกาจ</h5>
                        <p class="text-muted mb-0">MD12347</p>
                    </div>
                    <div>
                        <span class="badge bg-soft-purple text-purple">Consult</span>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">ความเชี่ยวชาญ:</span>
                        <span>Surgery</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">แผนก:</span>
                        <span>Surgery</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted"> ฿500/visit</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">การัน 30000:</span>
                        <span class="fw-medium">-</span>
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                    <span class="badge bg-soft-success text-success">Active</span>
                    <div>
                        <button class="btn btn-sm btn-light me-1" data-doctor-id="MD12347">
                            <i class="ri-pencil-line"></i>
                        </button>
                        <button class="btn btn-sm btn-light" data-doctor-id="MD12347">
                            <i class="ri-delete-bin-line text-danger"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<!-- Guarantee Modal -->
<div class="modal fade" id="guaranteeModal" tabindex="-1" aria-labelledby="guaranteeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="guaranteeModalLabel">ตั้งค่าการันตีแพทย์</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="ปิด"></button>
            </div>
            <div class="modal-body">
                <form id="guaranteeForm">
                    <input type="hidden" id="guaranteeDoctorId" name="guaranteeDoctorId">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="guaranteeType" class="form-label">ประเภทการันตี</label>
                            <select class="form-select" id="guaranteeType" name="guaranteeType">
                                <option value="daily">รายวัน</option>
                                <option value="hourly">รายชั่วโมง</option>
                                <option value="monthly">รายเดือน</option>
                                <option value="yearly">รายปี</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="guaranteeAmount" class="form-label">จำนวนเงินการันตี</label>
                            <div class="input-group">
                                <span class="input-group-text">฿</span>
                                <input type="number" class="form-control" id="guaranteeAmount" name="guaranteeAmount"
                                    min="0" value="100000">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <label for="effectiveDate" class="form-label">วันที่มีผล</label>
                            <input type="date" class="form-control" id="effectiveDate" name="effectiveDate">
                        </div>

                        <div class="col-md-12">
                            <div class="form-check form-switch form-switch-lg" dir="ltr">
                                <input type="checkbox" class="form-check-input" id="customSwitchsizelg"
                                    name="customSwitchsizelg" checked>
                                <label class="form-check-label" for="customSwitchsizelg">กำหนดช่วงเวลาการันตี</label>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="startdate" class="form-label">วันที่เริ่มต้น</label>
                            <input type="date" class="form-control" id="startdate" name="startdate">
                        </div>
                        <div class="col-md-6">
                            <label for="enddate" class="form-label">วันที่สิ้นสุด</label>
                            <input type="date" class="form-control" id="enddate" name="enddate">
                        </div>
                        <div class="col-md-12">
                            <label class="form-label">ประเภทบริการ</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="opdCheckbox" name="serviceType"
                                    value="OPD" checked>
                                <label class="form-check-label" for="opdCheckbox">
                                    OPD (ผู้ป่วยนอก)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="ipdCheckbox" name="serviceType"
                                    value="IPD" checked>
                                <label class="form-check-label" for="ipdCheckbox">
                                    IPD (ผู้ป่วยใน)
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" id="saveGuaranteeBtn">บันทึก</button>
            </div>
        </div>
    </div>
</div>


<!-- Add Doctor Modal -->
<div class="modal fade" id="addDoctorModal" tabindex="-1" aria-labelledby="addDoctorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDoctorModalLabel">เพิ่มแพทย์ใหม่</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addDoctorForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="doctorCode" class="form-label">แพทย์</label>
                            <input type="text" class="form-control" id="doctorCode" placeholder="MD12345">
                        </div>
                        <div class="col-md-6">
                            <label for="doctorType" class="form-label">ประเภทแพทย์</label>
                            <select class="form-select" id="doctorType">
                                <option value="fulltime">Fulltime</option>
                                <option value="parttime">Parttime</option>
                                <option value="consult">Consult</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="doctorTitle" class="form-label">คำนำหน้า</label>
                            <select class="form-select" id="doctorTitle">
                                <option value="Dr.">Dr.</option>
                                <option value="Prof.Dr.">Prof.Dr.</option>
                                <option value="Assoc.Prof.Dr.">Assoc.Prof.Dr.</option>
                                <option value="Asst.Prof.Dr.">Asst.Prof.Dr.</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="doctorName" class="form-label">ชื่อ-นามสู้</label>
                            <input type="text" class="form-control" id="doctorName" placeholder="ชื่อ นามสู้">
                        </div>
                        <div class="col-md-6">
                            <label for="specialty" class="form-label">ความเชี่ยวชาญ</label>
                            <input type="text" class="form-control" id="specialty">
                        </div>
                        <div class="col-md-6">
                            <label for="department" class="form-label">แผนก</label>
                            <input type="text" class="form-control" id="department">
                        </div>
                        <div class="col-md-6">
                            <label for="feeType" class="form-label">ประเภทค่าตอบแทน</label>
                            <select class="form-select" id="feeType">
                                <option value="percentage">เปอร์เซ็นต์</option>
                                <option value="fixed">ต่อครั้ง</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="feeRate" class="form-label">ค่าตอบแทน</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="feeRate">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="guarantee" class="form-label">การัน 30000</label>
                            <div class="input-group">
                                <span class="input-group-text">฿</span>
                                <input type="number" class="form-control" id="guarantee">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">สถานะ</label>
                            <select class="form-select" id="status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">ยกเลิก</button>
                <button type="button" class="btn btn-primary" id="saveDoctorBtn">บันทึก</button>
            </div>
        </div>
    </div>
</div>



<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize modal
        const addDoctorModal = new bootstrap.Modal(document.getElementById('addDoctorModal'));

        // Add doctor button click
        document.getElementById('addDoctorBtn').addEventListener('click', function () {
            document.getElementById('addDoctorForm').reset();
            document.getElementById('addDoctorModalLabel').textContent = 'เพิ่มแพทย์ใหม่';
            addDoctorModal.show();
        });

        // Edit doctor buttons
        document.querySelectorAll('[data-doctor-id]').forEach(button => {
            if (button.classList.contains('btn-light') && button.querySelector('.ri-pencil-line')) {
                button.addEventListener('click', function () {
                    const doctorId = this.getAttribute('data-doctor-id');
                    document.getElementById('addDoctorModalLabel').textContent = 'แก้ไขข้อมูลแพทย์';

                    // ในสถานการณ์<|im_start|> ควรจะขอข้อมูลจาก API
                    // แต่ในispielอย่าง应用查看จะใส่ข้อมูลispielอย่าง
                    if (doctorId === 'MD12345') {
                        document.getElementById('doctorCode').value = 'MD12345';
                        document.getElementById('doctorType').value = 'fulltime';
                        document.getElementById('doctorTitle').value = 'Dr.';
                        document.getElementById('doctorName').value = 'สมชาย ใจ';
                        document.getElementById('specialty').value = 'Internal Medicine';
                        document.getElementById('department').value = 'Internal Medicine';
                        document.getElementById('feeType').value = 'percentage';
                        document.getElementById('feeRate').value = '25';
                        document.getElementById('guarantee').value = '120000';
                        document.getElementById('status').value = 'active';
                    }
                    else if (doctorId === 'MD12346') {
                        document.getElementById('doctorCode').value = 'MD12346';
                        document.getElementById('doctorType').value = 'parttime';
                        document.getElementById('doctorTitle').value = 'Prof.Dr.';
                        document.getElementById('doctorName').value = 'ภาใจ';
                        document.getElementById('specialty').value = 'Cardiology';
                        document.getElementById('department').value = 'Cardiology';
                        document.getElementById('feeType').value = 'fixed';
                        document.getElementById('feeRate').value = '';
                        document.getElementById('guarantee').value = '80000';
                        document.getElementById('status').value = 'active';
                    }
                    else if (doctorId === 'MD12347') {
                        document.getElementById('doctorCode').value = 'MD12347';
                        document.getElementById('doctorType').value = 'consult';
                        document.getElementById('doctorTitle').value = 'Assoc.Prof.Dr.';
                        document.getElementById('doctorName').value = 'สมหญิง ใจ';
                        document.getElementById('specialty').value = 'Pediatrics';
                        document.getElementById('department').value = 'Surgery';
                        document.getElementById('feeType').value = 'fixed';
                        document.getElementById('feeRate').value = '';
                        document.getElementById('guarantee').value = '30000';
                        document.getElementById('status').value = 'active';
                    }

                    addDoctorModal.show();
                });
            }
        });

        // Save doctor button click
        document.getElementById('saveDoctorBtn').addEventListener('click', function () {
            const doctorCode = document.getElementById('doctorCode').value;
            const doctorType = document.getElementById('doctorType').value;
            const doctorTitle = document.getElementById('doctorTitle').value;
            const doctorName = document.getElementById('doctorName').value;
            const specialty = document.getElementById('specialty').value;
            const department = document.getElementById('department').value;
            const feeType = document.getElementById('feeType').value;
            const feeRate = document.getElementById('feeRate').value;
            const guarantee = document.getElementById('guarantee').value;
            const status = document.getElementById('status').value;

            // Send data to server
            fetch('/api/doctors', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    code: doctorCode,
                    type: doctorType,
                    title: doctorTitle,
                    name: doctorName,
                    specialty: specialty,
                    department: department,
                    feeType: feeType,
                    feeRate: feeRate,
                    guarantee: guarantee,
                    status: status
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addDoctorModal.hide();
                        alert('Doctor saved successfully!');
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while saving the doctor.');
                });
        });
    });
</script>

<script>
    // ตรวจสอบข้อมูลการันตี
    document.getElementById('saveGuaranteeBtn').addEventListener('click', function () {
        const doctorId = document.getElementById('guaranteeDoctorId').value;
        const guaranteeType = document.getElementById('guaranteeType').value;
        const guaranteeAmount = document.getElementById('guaranteeAmount').value;
        const effectiveDate = document.getElementById('effectiveDate').value;
        const startdate = document.getElementById('startdate').value;
        const enddate = document.getElementById('enddate').value;

        // ตรวจสอบประเภท
        const serviceTypes = [];
        if (document.getElementById('opdCheckbox').checked) {
            serviceTypes.push('OPD');
        }
        if (document.getElementById('ipdCheckbox').checked) {
            serviceTypes.push('IPD');
        }

        // ตรวจสอบข้อมูลจำเป็น
        if (!guaranteeAmount) {
            alert('กรุณากรอกข้อมูลจำนวนการันตีและวันที่มีผล');
            return;
        }

        // ส่งข้อมูล API
        fetch('/api/doctor-guarantees', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                doctorId: doctorId,
                guaranteeType: guaranteeType,
                guaranteeAmount: guaranteeAmount,
                effectiveDate: effectiveDate,
                startdate: startdate,
                enddate: enddate,
                serviceTypes: serviceTypes
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // ปิด Modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('guaranteeModal'));
                    modal.hide();

                    alert('บันทึกข้อมูลการันตีเรียบร้อยแล้ว');

                    // โหลดข้อมูลแพทย์ใหม่
                    fetch('/api/doctors')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                displayDoctors(data.data);
                            }
                        });
                } else {
                    alert('ข้อผิดพลาด: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('ข้อผิดพลาดในการบันทึกข้อมูลการันตี');
            });
    });

</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {

        // ฟังก์ชันเปิด/ปิด input date
        function toggleDateInputs() {
            const isChecked = document.getElementById('customSwitchsizelg').checked;
            document.getElementById('startdate').disabled = !isChecked;
            document.getElementById('expiryDate').disabled = !isChecked;
        }

        // เรียกใช้เมื่อโหลดหน้า
        window.addEventListener('DOMContentLoaded', toggleDateInputs);

        // เรียกใช้เมื่อมีการเปลี่ยนสถานะ checkbox
        document.getElementById('customSwitchsizelg').addEventListener('change', toggleDateInputs);



        // ดึงข้อมูลแพทย์จาก API
        fetch('/api/doctors')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayDoctors(data.data);
                } else {
                    console.error('ข้อผิดพลาดในการโหลดข้อมูลแพทย์:', data.message);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูลแพทย์: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการโหลดข้อมูลแพทย์');
            });
    });

    // แสดงข้อมูลแพทย์ในรูปแบบการ์ด
    function displayDoctors(doctors) {
        const container = document.getElementById('doctorCardsContainer');
        container.innerHTML = ''; // เคลียร์ข้อมูลเก่า

        if (!doctors || doctors.length === 0) {
            container.innerHTML = '<div class="col-12 text-center"><p>ไม่พบข้อมูลแพทย์</p></div>';
            return;
        }

        doctors.forEach(doctor => {
            // กำหนดประเภทแพทย์และสีของ badge
            let doctorType = 'Staff';
            let badgeClass = 'bg-soft-info text-info';

            if (doctor.doctor_type === 'fulltime') {
                doctorType = 'Fulltime';
                badgeClass = 'bg-soft-primary text-primary';
            } else if (doctor.doctor_type === 'parttime') {
                doctorType = 'Parttime';
                badgeClass = 'bg-soft-success text-success';
            } else if (doctor.doctor_type === 'consult') {
                doctorType = 'Consult';
                badgeClass = 'bg-soft-purple text-purple';
            }

            // สร้าง HTML สำหรับการ์ดแพทย์
            const doctorCard = document.createElement('div');
            doctorCard.className = 'col-lg-4 col-md-6 mb-3';
            doctorCard.innerHTML = `
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-sm me-3">
                                <div class="avatar-title bg-light text-primary rounded-circle">
                                    <i class="ri-user-3-line fs-20"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="mb-1">${doctor.title || ''} ${doctor.firstname} ${doctor.lastname}</h5>
                                <p class="text-muted mb-0">${doctor.code || doctor.id}</p>
                            </div>
                            <div>
                                <span class="badge ${badgeClass}">${doctorType}</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">ความเชี่ยวชาญ:</span>
                                <span>${doctor.professional || '-'}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">แผนก:</span>
                                <span>${doctor.location_id || '-'}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span class="text-muted">ส่วน:</span>
                                <span>${doctor.fee_rate ? doctor.fee_rate + '%' : '-'}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">การันตี:</span>
                                <span class="fw-medium">${doctor.guarantee ? '฿' + Number(doctor.guarantee).toLocaleString() : '-'}</span>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-soft-${doctor.statusflag === 'A' ? 'success' : 'danger'} text-${doctor.statusflag === 'A' ? 'success' : 'danger'}">
                                ${doctor.statusflag === 'A' ? 'Active' : 'Inactive'}
                            </span>
                            <div>
                                <button class="btn btn-sm btn-light me-1 edit-doctor" data-doctor-id="${doctor.id}">
                                    <i class="ri-pencil-line"></i>
                                </button>
                                <button class="btn btn-sm btn-light delete-doctor" data-doctor-id="${doctor.id}">
                                    <i class="ri-delete-bin-line text-danger"></i>
                                </button>

                                <button class="btn btn-dark guaranteen-doctor" data-doctor-id="${doctor.id}">
                                    <i class="ri-calculator-line fs-18  text-white"></i> ตั้งค่าการันตีแพทย์
                                </button>

                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(doctorCard);
        });

        // เพิ่ม Event Listeners สำหรับปุ่มแก้ไขและลบ
        document.querySelectorAll('.edit-doctor').forEach(button => {
            button.addEventListener('click', function () {
                const doctorId = this.getAttribute('data-doctor-id');
                editDoctor(doctorId);
            });
        });

        document.querySelectorAll('.delete-doctor').forEach(button => {
            button.addEventListener('click', function () {
                const doctorId = this.getAttribute('data-doctor-id');
                deleteDoctor(doctorId);
            });
        });

        document.querySelectorAll('.guaranteen-doctor').forEach(button => {
            button.addEventListener('click', function () {
                const doctorId = this.getAttribute('data-doctor-id');
                showGuaranteeModal(doctorId);
            });
        });

    }



    function showGuaranteeModal(doctorId) {
        // ข้อมูลแพทย์จาก API
        fetch(`/api/users/${doctorId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const doctor = data.data;

                    // กำหนดชื่อแพทย์ใน Modal
                    document.getElementById('guaranteeModalLabel').textContent =
                        `ตั้งค่าการันตีแพทย์: ${doctor.title || ''} ${doctor.firstname} ${doctor.lastname}`;

                    // กำหนดค่า doctor ID ใน form
                    document.getElementById('guaranteeDoctorId').value = doctorId;

                    // เปิด Modal
                    const modal = new bootstrap.Modal(document.getElementById('guaranteeModal'));
                    modal.show();
                } else {
                    console.error('ข้อผิดพลาดในการโหลดข้อมูลแพทย์:', data.message);
                    alert('ข้อผิดพลาดในการโหลดข้อมูลแพทย์: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('ข้อผิดพลาดในการโหลดข้อมูลแพทย์');
            });
    }


    // ฟังก์ชันสำหรับแก้ไขข้อมูลแพทย์
    function editDoctor(doctorId) {
        // ดึงข้อมูลแพทย์จาก API
        fetch(`/api/doctors/${doctorId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // เปิด Modal และใส่ข้อมูลแพทย์
                    const doctor = data.data;
                    $('#doctorCode').val(doctor.code || doctor.id);
                    $('#doctorType').val(doctor.doctor_type || 'fulltime');
                    $('#doctorTitle').val(doctor.title || 'Dr.');
                    $('#doctorName').val(doctor.firstname + ' ' + doctor.lastname);
                    $('#specialty').val(doctor.specialty || '');
                    $('#department').val(doctor.department || '');
                    $('#feeType').val(doctor.fee_type || 'percentage');
                    $('#feeRate').val(doctor.fee_rate || '');
                    $('#guarantee').val(doctor.guarantee || '');
                    $('#status').val(doctor.statusflag === 'A' ? 'active' : 'inactive');

                    // เปิด Modal
                    const modal = new bootstrap.Modal(document.getElementById('addDoctorModal'));
                    modal.show();
                } else {
                    console.error('ข้อผิดพลาดในการโหลดข้อมูลแพทย์:', data.message);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูลแพทย์: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการโหลดข้อมูลแพทย์');
            });
    }

    // ฟังก์ชันสำหรับลบข้อมูลแพทย์
    function deleteDoctor(doctorId) {
        if (confirm('คุณต้องการลบข้อมูลแพทย์นี้ใช่หรือไม่?')) {
            fetch(`/api/doctors/${doctorId}`, {
                method: 'DELETE'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('ลบข้อมูลแพทย์เรียบร้อยแล้ว');
                        // โหลดข้อมูลแพทย์ใหม่
                        fetch('/api/doctors')
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    displayDoctors(data.data);
                                }
                            });
                    } else {
                        console.error('ข้อผิดพลาดในการลบข้อมูลแพทย์:', data.message);
                        alert('เกิดข้อผิดพลาดในการลบข้อมูลแพทย์: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการลบข้อมูลแพทย์');
                });
        }
    }
</script>
<!-- Add custom CSS for the dashboard -->
<style>
    .page-content {
        background-color: white;
    }

    .card-border {
        border: 1px solid #e9ebec;
        border-radius: 0.25rem;
    }

    .card-body {
        border: 1px solid #e9ebec;
    }

    .nav-tabs-custom {
        background-color: #f6fbfd;
    }

    .avatar-title {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }

    .text-purple {
        color: #7367f0 !important;
    }

    .bg-soft-purple {
        background-color: rgba(115, 103, 240, 0.1) !important;
    }
</style>

<!-- Add custom CSS -->
<style>
    .nav-tabs-custom .nav-link.active {
        color: #405189;
        background-color: #fff;
        border-bottom: 2px solid #405189;
    }


    .search-box {
        position: relative;
    }

    .search-box .search-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 10px;
        color: #878a99;
    }

    .bg-soft-purple {
        background-color: rgba(115, 103, 240, 0.1) !important;
    }

    .text-purple {
        color: #7367f0 !important;
    }
</style>

</div>