const express = require('express');
const path = require('path');
const router = express.Router();
const authController = require('../controllers/authController');
const aiController = require('../controllers/aiController');
const userController = require('../controllers/userController');
const dfController = require('../controllers/dfController');

// Authentication middleware
const isAuthenticated = (req, res, next) => {
  if (req.session && req.session.user) {
    return next();
  }
  // Redirect to login page if not authenticated
  //test
  //Admin123#
  req.flash('error_msg', 'Please log in to access this page');
  return res.redirect('/login');
};



// Route for the home page
router.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../index.html'));
});



//router.get('/auth', authController.login);
router.get('/login', authController.showLoginPage);
router.post('/login', authController.login);
router.get('/signup', authController.renderSignup);
router.post('/signup', authController.signup);
router.get('/logout', authController.logout);

// Dashboard route
router.get('/', (req, res) => res.redirect('/doctor-fee-dashboard'));
// AI routes
router.get('/api/ai/summarize/:vn', aiController.summarizeByVN);



//Doctor Fee System Dashboard
router.get('/doctor-fee-dashboard',isAuthenticated, dfController.renderDFDashboard);
//Doctor Fee Management
router.get('/doctor-management',isAuthenticated, dfController.renderDFManagement);
//Doctor Fee Cal
router.get('/doctor-cal',isAuthenticated, dfController.renderDFCal);
//Doctor Fee guaranteen
router.get('/doctor-guaranteen',isAuthenticated, dfController.renderDFGuaranteen);
//Doctor Fee tax
router.get('/doctor-tax',isAuthenticated, dfController.renderDFTax);
//Doctor Fee report
router.get('/doctor-report',isAuthenticated, dfController.renderDFReport);

// User routes
router.get('/setupuser', userController.renderUserPage);
router.get('/api/users', userController.getAllUsers);
router.get('/api/users/:id', userController.getUserById);
router.post('/api/users', userController.createUser);
router.put('/api/users/:id', userController.updateUser);
router.delete('/api/users/:id', userController.deleteUser);
router.post('/api/users/:id/reset-password', userController.resetPassword);
router.get('/api/doctors', dfController.getAllDoctors);

// Doctor Guarantee API routes
router.post('/api/doctor-guarantees', dfController.createDoctorGuarantee);
router.get('/api/doctor-guarantees/doctor/:doctorId', dfController.getDoctorGuarantees);
router.get('/api/doctor-guarantees/:id', dfController.getDoctorGuaranteeById);
router.delete('/api/doctor-guarantees/:id', dfController.deleteDoctorGuarantee);

module.exports = router;






