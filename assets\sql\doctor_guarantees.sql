-- Table structure for doctor_guarantees
CREATE TABLE IF NOT EXISTS "public"."doctor_guarantees" (
  "id" SERIAL PRIMARY KEY,
  "doctor_id" INTEGER NOT NULL,
  "guarantee_type" VARCHAR(20) NOT NULL, -- 'daily', 'hourly', 'monthly', 'yearly'
  "guarantee_amount" NUMERIC(15,2) NOT NULL,
  "effective_date" DATE NOT NULL,
  "expiry_date" DATE,
  "service_types" VARCHAR(50), -- 'OPD', 'IPD', 'OPD,IPD'
  "status" VARCHAR(1) DEFAULT 'A', -- 'A' = Active, 'I' = Inactive
  "created_by" INTEGER,
  "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updated_by" INTEGER,
  "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT "fk_doctor_guarantees_doctor" FOREIGN KEY ("doctor_id") REFERENCES "users" ("id") ON DELETE CASCADE
);

-- Index for doctor_id
CREATE INDEX IF NOT EXISTS "idx_doctor_guarantees_doctor_id" ON "public"."doctor_guarantees" ("doctor_id");

-- Index for effective_date
CREATE INDEX IF NOT EXISTS "idx_doctor_guarantees_effective_date" ON "public"."doctor_guarantees" ("effective_date");