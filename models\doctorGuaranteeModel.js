const pool = require('../config/database');

const DoctorGuarantee = {
  // สร้างการันตีแพทย์
  create: async (guaranteeData, userId) => {
    try {
      const { doctorId, guaranteeType, guaranteeAmount, effectiveDate, startdate, enddate, serviceTypes } = guaranteeData;
      
      // แปลง array ของ serviceTypes เป็น string คั่นด้วยเครื่องหมาย comma
      const serviceTypesStr = Array.isArray(serviceTypes) ? serviceTypes.join(',') : serviceTypes;
      
      // ตรวจสอบว่าการันตีนี้มีอยู่แล้วหรือไม่
       
       const [checkResult] = await pool.execute(`SELECT id FROM doctor_guarantees 
        WHERE doctor_id = ? 
        AND guarantee_type = ?
        AND statusflag = 'A' `, [doctorId, guaranteeType]);
      
      console.log(checkResult.length)
      if (checkResult.length > 0) {
        // ถ้าการันตีนี้มีอยู่แล้ว ให้ return ข้อมูลว่ามีอยู่แล้วโดยไม่ต้อง update
        return {
          id: checkResult[0].id,
          exists: true,
          message: 'การันตีประเภทนี้มีอยู่แล้วในระบบ'
        };
      } else {
        // ถ้ายังไม่มีการันตีนี้
        //console.log({ doctorId, guaranteeType, guaranteeAmount, effectiveDate, startdate, enddate, serviceTypes });
        const [result] = await pool.execute(
       `INSERT INTO doctor_guarantees (
            doctor_id, guarantee_type, guarantee_amount, 
            effective_date, start_date, end_date, service_types, 
            created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          doctorId,
          guaranteeType,
          guaranteeAmount,
          effectiveDate,
          startdate,
          enddate,
          serviceTypesStr,
          userId
        ]
      );

      return {
        success: true,
        id: result.insertId,
        message: 'User created successfully'
      };
      }     
    
    } catch (error) {
      console.error('Error in create doctor guarantee:', error);
      throw error;
    }
  },
  
  // ดึงการันตีของแพทย์
  getByDoctorId: async (doctorId) => {
    try {
      const query = `
        SELECT dg.*, u.firstname, u.lastname, u.title
        FROM doctor_guarantees dg
        JOIN users u ON dg.doctor_id = u.id
        WHERE dg.doctor_id = $1
        AND dg.statusflag = 'A'
        ORDER BY dg.startdate DESC
      `;
      
      const result = await pool.query(query, [doctorId]);
      return result.rows;
    } catch (error) {
      console.error('Error in getByDoctorId:', error);
      throw error;
    }
  },
  
  // ดึงการันตีแยกตาม ID
  getById: async (id) => {
    try {
      const query = `
        SELECT dg.*, u.firstname, u.lastname, u.title
        FROM doctor_guarantees dg
        JOIN users u ON dg.doctor_id = u.id
        WHERE dg.id = $1
      `;
      
      const result = await pool.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error in getById:', error);
      throw error;
    }
  },
  
  // ลบการันตี (soft delete)
  delete: async (id, userId) => {
    try {
      const query = `
        UPDATE doctor_guarantees
        SET statusflag = 'I', updated_by = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING id
      `;
      
      const result = await pool.query(query, [userId, id]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error in delete doctor guarantee:', error);
      throw error;
    }
  },

    // ปรับปรุงการันตีแพทย์
  update: async (id, guaranteeData, userId) => {
    try {
      const { guaranteeType, guaranteeAmount, effectiveDate, expiryDate, serviceTypes } = guaranteeData;
      
      // แปลง array ของ serviceTypes เป็น string คั่นด้วยเครื่องหมาย comma
      const serviceTypesStr = Array.isArray(serviceTypes) ? serviceTypes.join(',') : serviceTypes;
      
      const updateQuery = `
        UPDATE doctor_guarantees
        SET guarantee_type = $1,
            guarantee_amount = $2,
            effective_date = $3,
            expiry_date = $4,
            service_types = $5,
            updated_by = $6,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $7
        RETURNING id
      `;
      
      const result = await pool.query(updateQuery, [
        guaranteeType,
        guaranteeAmount,
        effectiveDate || null,
        expiryDate || null,
        serviceTypesStr,
        userId,
        id
      ]);
      
      if (result.rows.length === 0) {
        return null; // ไม่พบข้อมูลการันตีที่ต้องการปรับปรุง
      }
      
      return result.rows[0];
    } catch (error) {
      console.error('Error in update doctor guarantee:', error);
      throw error;
    }
  }

};

module.exports = DoctorGuarantee;